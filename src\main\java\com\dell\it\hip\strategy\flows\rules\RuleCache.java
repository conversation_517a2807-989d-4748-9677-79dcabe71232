package com.dell.it.hip.strategy.flows.rules;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.rules.HIPRuleEntity;
import com.dell.it.hip.config.rules.Rule;
import com.dell.it.hip.config.rules.RuleRef;
import com.dell.it.hip.core.repository.HIPRuleRepository;
import com.dell.it.hip.util.redis.HIPRedisKeyUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
public class RuleCache implements RuleCacheService {

	private final StringRedisTemplate redisTemplate;
	private final HIPRuleRepository hipRuleRepository;
	private final ObjectMapper objectMapper;
	private final Map<String, Rule> cache = new ConcurrentHashMap<>();

	public RuleCache(StringRedisTemplate redisTemplate, HIPRuleRepository hipRuleRepository,
			ObjectMapper objectMapper) {
		this.redisTemplate = redisTemplate;
		this.hipRuleRepository = hipRuleRepository;
		this.objectMapper = objectMapper;
	}

	/** Preload and cache all rules for a given integration by key prefix. */
	public void refreshIntegrationRules(String serviceManager, String integration, String version) {
		String prefix = HIPRedisKeyUtil.flowRoutingRulePrefix(serviceManager, integration, version);
		List<Rule> rules = new ArrayList<>();
		// Remove any cached rules for this integration
		cache.keySet().removeIf(k -> k.startsWith(prefix));

		Set<String> redisKeys = redisTemplate.keys(prefix + "*");
		if (redisKeys != null) {
			for (String key : redisKeys) {
				String json = redisTemplate.opsForValue().get(key);
				if (json != null) {
					try {
						Rule rule = objectMapper.readValue(json, Rule.class);
						cache.put(key, rule);
						rules.add(rule);
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}
		// If not found in Redis, fallback to DB
		if (rules.isEmpty()) {
		List<HIPRuleEntity> dbEntities = hipRuleRepository.findByRuleKeyPrefix(prefix);
		for (HIPRuleEntity entity : dbEntities) {
			try {
				Rule rule = objectMapper.readValue(entity.getRuleJson(), Rule.class);
				cache.put(entity.getRuleKey(), rule);
			} catch (Exception e) {
				/* log parse error */}
		}
		}
	}

	/** Preload and cache the explicit list of rules provided by refs. */
	public void refreshExplicitRules(List<RuleRef> ruleRefs) {
		for (RuleRef ref : ruleRefs) {
			String key = HIPRedisKeyUtil.ruleKey(ref.getRuleName(), ref.getRuleVersion());
			// Remove any cached entry
			cache.remove(key);

			String json = redisTemplate.opsForValue().get(key);
			if (json != null) {
				try {
					Rule rule = objectMapper.readValue(json, Rule.class);
					cache.put(key, rule);
				} catch (Exception e) {
					/* log parse error */ }
			} else {
				HIPRuleEntity entity = hipRuleRepository.findByRuleKey(key);
				if (entity != null && entity.getRuleJson() != null) {
					try {
						Rule rule = objectMapper.readValue(entity.getRuleJson(), Rule.class);
						cache.put(key, rule);
					} catch (Exception e) {
						/* log parse error */ }
				}
			}
		}
	}

	// For FlowRoutingFlowStepStrategy
	public List<Rule> getRulesForIntegration(String serviceManager, String integration, String version) {
		String prefix = HIPRedisKeyUtil.flowRoutingRulePrefix(serviceManager, integration, version);
		Set<String> redisKeys = redisTemplate.keys(prefix + "*");
		List<Rule> rules = new ArrayList<>();
		if (redisKeys != null && !redisKeys.isEmpty()) {
			for (String key : redisKeys) {
				Rule cached = cache.get(key);
				if (cached != null) {
					rules.add(cached);
					continue;
				}
				String json = redisTemplate.opsForValue().get(key);
				if (json != null) {
					try {
						Rule rule = objectMapper.readValue(json, Rule.class);
						cache.put(key, rule);
						rules.add(rule);
					} catch (Exception e) {
						/* log parse error */ }
				}
			}
		}
		// DB fallback
        if (rules.isEmpty()) {
            List<HIPRuleEntity> dbEntities = hipRuleRepository.findByRuleKeyPrefix(prefix);
            for (HIPRuleEntity entity : dbEntities) {
                try {
                    Rule rule = objectMapper.readValue(entity.getRuleJson(), Rule.class);
                    cache.put(entity.getRuleKey(), rule);
                    rules.add(rule);
                } catch (Exception e) { /* log parse error */ }
            }
        }

		return rules;
	}

	// For MappingTransformerFlowStepStrategy & FlowTargetsRoutingFlowStepStrategy
	public List<Rule> getRulesByRefs(List<RuleRef> ruleRefs, boolean dbBacked) {
		List<Rule> rules = new ArrayList<>();
		for (RuleRef ref : ruleRefs) {
			String key = HIPRedisKeyUtil.ruleKey(ref.getRuleName(), ref.getRuleVersion());
			Rule cached = cache.get(key);
			if (cached != null) {
				rules.add(cached);
				continue;
			}
			String json = redisTemplate.opsForValue().get(key);
			if (json != null) {
				try {
					Rule rule = objectMapper.readValue(json, Rule.class);
					cache.put(key, rule);
					rules.add(rule);
				} catch (Exception e) {
					/* log parse error */ }
			} else if (dbBacked) {
				HIPRuleEntity entity = hipRuleRepository.findByRuleKey(key);
				if (entity != null && entity.getRuleJson() != null) {
					try {
						Rule rule = objectMapper.readValue(entity.getRuleJson(), Rule.class);
						cache.put(key, rule);
						rules.add(rule);
					} catch (Exception e) {
						/* log parse error */ }
				}
			}
		}
		return rules;
	}

	// Optionally, preload cache
	public void preloadRulesForIntegration(String serviceManager, String integration, String version) {
		getRulesForIntegration(serviceManager, integration, version);
	}

	public void preloadExplicitRules(List<RuleRef> ruleRefs, boolean dbBacked) {
		getRulesByRefs(ruleRefs, dbBacked);
	}

	// Explicit refresh by key or prefix
	public void refreshRule(String key) {
		cache.remove(key);
	}

	public void clear() {
		cache.clear();
	}
}