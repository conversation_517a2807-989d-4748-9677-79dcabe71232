package com.dell.it.hip.strategy.flows.rules;

import java.util.List;

import com.dell.it.hip.config.rules.Rule;
import com.dell.it.hip.config.rules.RuleRef;

/**
 * Interface for rule caching services.
 * Provides a common contract for both Redis-enabled and database-only rule caching implementations.
 */
public interface RuleCacheService {

    /** Preload and cache all rules for a given integration by key prefix. */
    void refreshIntegrationRules(String serviceManager, String integration, String version);

    /** Preload and cache the explicit list of rules provided by refs. */
    void refreshExplicitRules(List<RuleRef> ruleRefs);

    /** Get rules for integration (used by FlowRoutingFlowStepStrategy). */
    List<Rule> getRulesForIntegration(String serviceManager, String integration, String version);

    /** Get rules by references (used by MappingTransformerFlowStepStrategy & FlowTargetsRoutingFlowStepStrategy). */
    List<Rule> getRulesByRefs(List<RuleRef> ruleRefs, boolean dbBacked);

    /** Preload rules for integration. */
    void preloadRulesForIntegration(String serviceManager, String integration, String version);

    /** Preload explicit rules. */
    void preloadExplicitRules(List<RuleRef> ruleRefs, boolean dbBacked);

    /** Refresh rule by key. */
    void refreshRule(String key);

    /** Clear all cached rules. */
    void clear();
}
