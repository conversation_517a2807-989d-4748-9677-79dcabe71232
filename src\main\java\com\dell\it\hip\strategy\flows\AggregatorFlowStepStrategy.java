package com.dell.it.hip.strategy.flows;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.AggregatorFlowStepConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.validation.MessageFormatDetector;

import lombok.Data;

/**
 * Aggregates messages in a cluster-wide, header/grouped way.
 */
@Component("aggregator")
@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
public class AggregatorFlowStepStrategy extends AbstractFlowStepStrategy {

    private final RedissonClient redissonClient;
    private final WiretapService wiretapService;

    public AggregatorFlowStepStrategy(RedissonClient redissonClient, WiretapService wiretapService) {
        this.redissonClient = redissonClient;
        this.wiretapService = wiretapService;
    }

    @Override
    public String getType() { return "aggregator"; }

    @Override
    public List<Message<?>> doExecute(
            Message<?> message,
            FlowStepConfigRef stepConfigRef,
            HIPIntegrationDefinition def
    ) throws Exception {
        AggregatorFlowStepConfig config = (AggregatorFlowStepConfig) def.getConfig(stepConfigRef.getPropertyRef(), AggregatorFlowStepConfig.class);
        
        if (config == null) {
            emitWiretapError(message, def, stepConfigRef, "AggregatorFlowStepConfig must not be null config for: " + stepConfigRef.getPropertyRef());
            return Collections.emptyList();
        }

        // === DocType/dataFormat detection ===
        String docTypeHeader = (String) message.getHeaders().get("hip.source.docType");
        String docTypeName = null, docTypeVersion = null;
        if (docTypeHeader != null && docTypeHeader.contains(":")) {
            String[] parts = docTypeHeader.split(":", 2);
            docTypeName = parts[0];
            docTypeVersion = parts[1];
        }
        String dataFormat = (String) message.getHeaders().get("hip.source.dataFormat");
        if (dataFormat == null)
            dataFormat = MessageFormatDetector.detect(String.valueOf(message.getPayload()));

        // === Find matching docType config or default ===
        AggregatorFlowStepConfig.DocTypeAggregatorConfig dtConfig = findMatchingDocTypeConfig(config, docTypeName, docTypeVersion, dataFormat);
        AggregatorFlowStepConfig.DefaultAggregatorConfig defaultCfg = config.getDefaultConfig();

        AggregatorFlowStepConfig.AggregatorBehavior behavior = dtConfig != null
                ? dtConfig.getBehavior()
                : (defaultCfg != null ? defaultCfg.getBehavior() : AggregatorFlowStepConfig.AggregatorBehavior.AGGREGATE);

        int batchSize = dtConfig != null ? dtConfig.getBatchSize() : (defaultCfg != null ? defaultCfg.getBatchSize() : 10);
        long batchTimeoutMs = dtConfig != null ? dtConfig.getBatchTimeoutMs() : (defaultCfg != null ? defaultCfg.getBatchTimeoutMs() : 30000);
        List<String> groupByHeaders = dtConfig != null ? dtConfig.getGroupByHeaders() : (defaultCfg != null ? defaultCfg.getGroupByHeaders() : null);
        List<String> preserveHeaders = dtConfig != null ? dtConfig.getPreserveHeaders() : (defaultCfg != null ? defaultCfg.getPreserveHeaders() : null);
        String aggregateTraceHeaderName = dtConfig != null ? dtConfig.getAggregateTraceHeaderName() : (defaultCfg != null ? defaultCfg.getAggregateTraceHeaderName() : null);

        // === Behavior handling ===
        if (behavior == AggregatorFlowStepConfig.AggregatorBehavior.SKIP) {
            wiretapService.tap(message, def, stepConfigRef, "info", "Aggregator: SKIP for docType " + docTypeHeader);
            return Collections.singletonList(message);
        }
        if (behavior == AggregatorFlowStepConfig.AggregatorBehavior.TERMINATE) {
            wiretapService.tap(message, def, stepConfigRef, "terminated", "Aggregator: TERMINATE for docType " + docTypeHeader);
            return Collections.emptyList();
        }

        // === AGGREGATE ===
        String groupKey = buildGroupKey(groupByHeaders, message);
        String mapKey = buildAggregateMapKey(def, stepConfigRef, docTypeName, docTypeVersion, dataFormat, groupKey);

        RMapCache<String, AggregationState> batchMap = redissonClient.getMapCache(mapKey);

        // Lock for this group
        String lockKey = mapKey + ":lock";
        var lock = redissonClient.getLock(lockKey);
        lock.lock(5, TimeUnit.SECONDS);
        try {
            AggregationState state = batchMap.get(groupKey);
            if (state == null) {
                state = new AggregationState();
                state.setFirstMessageTime(Instant.now().toEpochMilli());
                state.setHeaders(message.getHeaders());
            }
            state.addMessage(message);
            batchMap.put(groupKey, state, batchTimeoutMs, TimeUnit.MILLISECONDS);

            // Release condition 1: Size threshold met
            if (state.getMessages().size() >= batchSize) {
                batchMap.remove(groupKey);
                wiretapService.tap(message, def, stepConfigRef, "info", "Aggregator: Batch size reached for group " + groupKey);
                return List.of(buildAggregatedMessage(state, preserveHeaders, aggregateTraceHeaderName));
            }

            // Release condition 2: Timeout expired
            long elapsed = Instant.now().toEpochMilli() - state.getFirstMessageTime();
            if (elapsed >= batchTimeoutMs) {
                batchMap.remove(groupKey);
                wiretapService.tap(message, def, stepConfigRef, "info", "Aggregator: Batch timeout for group " + groupKey);
                return List.of(buildAggregatedMessage(state, preserveHeaders, aggregateTraceHeaderName));
            }

            // Not enough for batch yet
            return Collections.emptyList();
        } finally {
            lock.unlock();
        }
    }

    private Message<?> buildAggregatedMessage(
            AggregationState state,
            List<String> preserveHeaders,
            String aggregateTraceHeaderName
    ) {
        // 1. Aggregate payloads (customize per format if you wish)
        String payload = state.getMessages().stream()
                .map(msg -> msg.getPayload().toString())
                .collect(Collectors.joining(",\n"));

        // 2. Aggregate headers
        Map<String, Object> aggregatedHeaders = aggregateHeaders(state.getMessages(), preserveHeaders, aggregateTraceHeaderName);

        return MessageBuilder.withPayload(payload)
                .copyHeaders(aggregatedHeaders)
                .setHeader("HIP.aggregatedMessageCount", state.getMessages().size())
                .build();
    }

    private String buildAggregateMapKey(HIPIntegrationDefinition def, FlowStepConfigRef stepConfigRef,
                                        String docType, String docTypeVersion, String dataFormat, String groupKey) {
        // Unique aggregation bucket per docType/docTypeVersion/dataFormat/group
        return String.join(":", "hip:aggregate",
                def.getServiceManagerName(),
                def.getHipIntegrationName(),
                def.getVersion(),
                stepConfigRef.getPropertyRef(),
                docType != null ? docType : "defaultDoc",
                docTypeVersion != null ? docTypeVersion : "defaultVer",
                dataFormat != null ? dataFormat : "defaultFmt",
                groupKey != null ? groupKey : "defaultGroup"
        );
    }

    private String buildGroupKey(List<String> groupByHeaders, Message<?> msg) {
        if (groupByHeaders == null || groupByHeaders.isEmpty()) return "default";
        StringBuilder sb = new StringBuilder();
        for (String header : groupByHeaders) {
            Object val = msg.getHeaders().get(header);
            sb.append(header).append("=").append(val != null ? val : "null").append("|");
        }
        return sb.toString();
    }

    private Map<String, Object> aggregateHeaders(
            List<Message<?>> batch,
            List<String> preserveHeaders,
            String aggregateTraceHeaderName
    ) {
        Map<String, Object> aggregateHeaders = new HashMap<>();
        if (batch.isEmpty()) return aggregateHeaders;

        aggregateHeaders.putAll(batch.get(0).getHeaders());
        Set<String> allHeaderNames = batch.stream()
                .flatMap(msg -> msg.getHeaders().keySet().stream())
                .collect(Collectors.toSet());

        for (String header : allHeaderNames) {
            List<Object> values = batch.stream()
                    .map(m -> m.getHeaders().get(header))
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (values.size() == 1) {
                aggregateHeaders.put(header, values.get(0));
            } else if ("traceparent".equalsIgnoreCase(header)) {
                aggregateHeaders.put(aggregateTraceHeaderName != null ? aggregateTraceHeaderName : "HIP.traceParents", values);
            } else {
                if (preserveHeaders != null && preserveHeaders.contains(header)) {
                    aggregateHeaders.put("HIP.aggregatedHeaders." + header, values);
                }
            }
        }
        aggregateHeaders.put("HIP.aggregatedMessageCount", batch.size());
        return aggregateHeaders;
    }

    // === docType config matching ===
    private AggregatorFlowStepConfig.DocTypeAggregatorConfig findMatchingDocTypeConfig(AggregatorFlowStepConfig config, String docTypeName, String docTypeVersion, String dataFormat ) {
        if (config.getDocTypeConfigs() == null) return null;
        for (AggregatorFlowStepConfig.DocTypeAggregatorConfig dtConfig : config.getDocTypeConfigs()) {
            if (dtConfig.getName().equalsIgnoreCase(docTypeName)
                    && (dtConfig.getVersion() == null || dtConfig.getVersion().equalsIgnoreCase(docTypeVersion))
                    && (dtConfig.getDataFormat() == null || dtConfig.getDataFormat().equalsIgnoreCase(dataFormat))) {
                return dtConfig;
            }
        }
        return null;
    }

    private void emitWiretapError(Message<?> message, HIPIntegrationDefinition def, FlowStepConfigRef stepConfigRef, String errorMsg) {
        wiretapService.tap(message, def, stepConfigRef, "error", errorMsg);
        TransactionLoggingUtil.logError(message, def, stepConfigRef, "SplitterError", errorMsg);
    }

    /** Inner class: tracks aggregation state per group */
    @Data
    public static class AggregationState implements java.io.Serializable {
        private List<Message<?>> messages = new ArrayList<>();
        private Map<String, Object> headers = new HashMap<>();
        private long firstMessageTime;
        public void addMessage(Message<?> msg) { this.messages.add(msg); }
    }
}