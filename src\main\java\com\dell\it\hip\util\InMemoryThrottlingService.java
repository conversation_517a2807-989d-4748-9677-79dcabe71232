package com.dell.it.hip.util;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import com.dell.it.hip.core.HIPIntegrationRuntimeServiceInterface;

/**
 * In-memory implementation of ThrottlingService for environments where Redis is disabled.
 * This implementation provides basic throttling functionality using in-memory storage.
 * Note: This is suitable for single-instance deployments or testing environments.
 * For production multi-instance deployments, Redis-backed implementation should be used.
 */
@Service
@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "false", matchIfMissing = true)
public class InMemoryThrottlingService implements ThrottlingService {

    private static final Logger logger = LoggerFactory.getLogger(InMemoryThrottlingService.class);

    @Autowired
    private HIPIntegrationRuntimeServiceInterface runtimeService;

    // In-memory storage for throttle counters
    private final Map<String, ThrottleBucket> buckets = new ConcurrentHashMap<>();

    @Override
    public boolean tryConsumeToken(String serviceManagerName, String integrationId, String integrationVersion,
                                   String adapterId, ThrottleSettings settings) {
        if (settings == null || !settings.isEnabled()) {
            return true;
        }

        String bucketKey = createBucketKey(serviceManagerName, integrationId, integrationVersion, adapterId);
        ThrottleBucket bucket = buckets.computeIfAbsent(bucketKey, k -> new ThrottleBucket());

        return bucket.tryConsume(settings);
    }

    @Override
    public void resetThrottle(String serviceManagerName, String integrationId, String integrationVersion, String adapterId) {
        String bucketKey = createBucketKey(serviceManagerName, integrationId, integrationVersion, adapterId);
        buckets.remove(bucketKey);
        logger.info("Reset throttle for {}:{}:{}:{}", serviceManagerName, integrationId, integrationVersion, adapterId);
    }

    @Override
    public void updateThrottle(String serviceManagerName, String integrationId, String integrationVersion,
                               String adapterId, ThrottleSettings settings) {
        // Update the throttle settings in the runtime service
        runtimeService.updateThrottle(serviceManagerName, integrationId, integrationVersion, adapterId, settings);
        
        // Reset the bucket to apply new settings immediately
        resetThrottle(serviceManagerName, integrationId, integrationVersion, adapterId);
        
        logger.info("Updated throttle for {}:{}:{}:{} with settings: {}", 
                   serviceManagerName, integrationId, integrationVersion, adapterId, settings);
    }

    @Override
    public ThrottleSettings getThrottleSettings(String serviceManagerName, String integrationId,
                                                String integrationVersion, String adapterId) {
        return runtimeService.getThrottleSettings(serviceManagerName, integrationId, integrationVersion, adapterId);
    }

    private String createBucketKey(String serviceManagerName, String integrationId, String integrationVersion, String adapterId) {
        return serviceManagerName + ":" + integrationId + ":" + integrationVersion + ":" + adapterId;
    }

    /**
     * Simple token bucket implementation for in-memory throttling.
     */
    private static class ThrottleBucket {
        private volatile long windowStart = 0;
        private final AtomicInteger count = new AtomicInteger(0);

        public synchronized boolean tryConsume(ThrottleSettings settings) {
            long now = Instant.now().getEpochSecond();
            long windowDuration = settings.getPeriodSeconds();
            
            // Check if we need to reset the window
            if (now >= windowStart + windowDuration) {
                windowStart = now;
                count.set(0);
            }
            
            // Try to consume a token
            int currentCount = count.incrementAndGet();
            boolean allowed = currentCount <= settings.getMaxMessagesPerPeriod();
            
            if (!allowed) {
                // Decrement back since we couldn't consume
                count.decrementAndGet();
            }
            
            return allowed;
        }
    }
}
