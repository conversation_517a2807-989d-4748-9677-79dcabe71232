spring:
  # Auto-configuration exclusions for tests
  autoconfigure:
    exclude:
      - org.redisson.spring.starter.RedissonAutoConfigurationV2
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.data.redis.RedisHealthContributorAutoConfiguration

  # Test database configuration (H2 in-memory for unit tests)
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA configuration for tests
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
    show-sql: false
  
  # Redis configuration - disabled for tests to avoid connection issues
  data:
    redis:
      enabled: false
      host: localhost
      port: 6379
      timeout: 2000ms
      url: redis://localhost:6379
  
  # Kafka configuration (will be overridden by TestContainers)
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: hip-test-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      auto-offset-reset: earliest

# Kafka configuration for actmon service
kafka:
  actmon:
    producer:
      topic: hip-actmon-test

# Configuration server settings for tests
configserver_uri: http://localhost:8888
configproperties_sheet_name: hip-services

# Environment variables for tests
SPRING_PROFILES_ACTIVE: test

# HIP test configuration
hip:
  health:
    external-apis: []
    sftp-servers: []

integration.store.type : JPA

# Environment variables for HIP services
hip_environment: test
service.manager.name: TestServiceManager

  
# Test-specific settings
test: 
  containers: 
     startup-timeout: 120
     cleanup-enabled: true

# Logging configuration for tests
logging:
  level:
    com.dell.it.hip: DEBUG
    org.springframework.test: INFO
    org.testcontainers: INFO
    org.springframework.data.redis: DEBUG
    org.springframework.kafka: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Management endpoints for testing
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
