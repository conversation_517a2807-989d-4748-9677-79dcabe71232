package com.dell.it.hip.core;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.util.ThrottleSettings;

/**
 * Database-only implementation of HIPIntegrationRuntimeService for environments where Redis is disabled.
 * This implementation provides the same interface but uses in-memory storage instead of Redis.
 * Note: This is suitable for single-instance deployments or testing environments.
 * For production multi-instance deployments, Redis-backed implementation should be used.
 */
@Service
@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "false", matchIfMissing = true)
public class DatabaseOnlyHIPIntegrationRuntimeService implements HIPIntegrationRuntimeServiceInterface {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseOnlyHIPIntegrationRuntimeService.class);

    @Value("${service.manager.name}")
    private String serviceManagerName;

    // In-memory storage for status and throttle settings
    private final Map<String, IntegrationStatus> statusCache = new ConcurrentHashMap<>();
    private final Map<String, ThrottleSettings> throttleCache = new ConcurrentHashMap<>();

    // --- Status management (integration-level, not adapter-specific for now) ---

    @Override
    public void updateHIPIntegrationStatus(String serviceManagerName, String hipIntegrationName, String version, IntegrationStatus status) {
        try {
            String key = statusKey(serviceManagerName, hipIntegrationName, version);
            statusCache.put(key, status);
            logger.info("[RUNTIME] Updated HIPIntegrationStatus: {}:{}:{} = {}", serviceManagerName, hipIntegrationName, version, status);
        } catch (Exception ex) {
            logger.error("Failed to update HIPIntegration status for {}:{}:{} - {}", serviceManagerName, hipIntegrationName, version, ex.getMessage(), ex);
        }
    }

    @Override
    public IntegrationStatus getHIPIntegrationStatus(String serviceManagerName, String hipIntegrationName, String version) {
        String key = statusKey(serviceManagerName, hipIntegrationName, version);
        IntegrationStatus status = statusCache.get(key);
        return status != null ? status : IntegrationStatus.UNREGISTERED;
    }

    // --- Throttle management (adapter-level, serviceManager-aware) ---

    @Override
    public void updateThrottle(String serviceManagerName, String integrationName, String version, String adapterId, ThrottleSettings settings) {
        String key = throttleKey(serviceManagerName, integrationName, version, adapterId);
        try {
            if (settings != null) {
                throttleCache.put(key, settings);
                logger.info("[RUNTIME] Throttle updated for {}:{}:{}:{}: {}", serviceManagerName, integrationName, version, adapterId, settings);
            } else {
                throttleCache.remove(key);
                logger.info("[RUNTIME] Throttle removed for {}:{}:{}:{}", serviceManagerName, integrationName, version, adapterId);
            }
        } catch (Exception e) {
            logger.error("Failed to update throttle settings for {}:{}:{}:{} - {}", serviceManagerName, integrationName, version, adapterId, e.getMessage(), e);
        }
    }

    @Override
    public ThrottleSettings getThrottleSettings(String serviceManagerName, String integrationName, String version, String adapterId) {
        String key = throttleKey(serviceManagerName, integrationName, version, adapterId);
        return throttleCache.get(key);
    }

    // --- Cleanup ---

    @Override
    public void removeAllRuntimeState(String serviceManagerName, String hipIntegrationName, String version) {
        try {
            // Remove status
            String statusKey = statusKey(serviceManagerName, hipIntegrationName, version);
            statusCache.remove(statusKey);
            
            // Remove all throttle settings for this integration
            String throttlePrefix = throttleKeyPrefix(serviceManagerName, hipIntegrationName, version);
            throttleCache.entrySet().removeIf(entry -> entry.getKey().startsWith(throttlePrefix));
            
            logger.info("[RUNTIME] All runtime state removed for {}:{}:{}", serviceManagerName, hipIntegrationName, version);
        } catch (Exception e) {
            logger.error("Failed to remove runtime state for {}:{}:{} - {}", serviceManagerName, hipIntegrationName, version, e.getMessage(), e);
        }
    }

    // --- Helper methods for key generation ---

    private String statusKey(String serviceManagerName, String hipIntegrationName, String version) {
        return "hip:runtime:status:" + serviceManagerName + ":" + hipIntegrationName + ":" + version;
    }

    private String throttleKey(String serviceManagerName, String integrationName, String version, String adapterId) {
        return "hip:runtime:throttle:" + serviceManagerName + ":" + integrationName + ":" + version + ":" + adapterId;
    }

    private String throttleKeyPrefix(String serviceManagerName, String integrationName, String version) {
        return "hip:runtime:throttle:" + serviceManagerName + ":" + integrationName + ":" + version + ":";
    }
}
