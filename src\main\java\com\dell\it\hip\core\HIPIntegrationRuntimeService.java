package com.dell.it.hip.core;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.util.ThrottleSettings;
import com.dell.it.hip.util.redis.HIPRedisKeyUtil;
import com.fasterxml.jackson.databind.ObjectMapper;



@Service
@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
public class HIPIntegrationRuntimeService implements HIPIntegrationRuntimeServiceInterface {

    private static final Logger logger = LoggerFactory.getLogger(HIPIntegrationRuntimeService.class);

    private final StringRedisTemplate redisTemplate;
    private final ObjectMapper objectMapper;

    @Value("${service.manager.name}")
    private String serviceManagerName; // default injected, but should always be passed for clarity

    public HIPIntegrationRuntimeService(StringRedisTemplate redisTemplate, ObjectMapper objectMapper) {
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
    }

    // --- Status management (integration-level, not adapter-specific for now) ---

    public void updateHIPIntegrationStatus(String serviceManagerName, String hipIntegrationName, String version, IntegrationStatus status) {
        try {
            String key = HIPRedisKeyUtil.statusKey(serviceManagerName, hipIntegrationName, version);
            redisTemplate.opsForValue().set(key, status.name());
            logger.info("[RUNTIME] Updated HIPIntegrationStatus: {}:{}:{} = {}", serviceManagerName, hipIntegrationName, version, status);
        } catch (Exception ex) {
            logger.error("Failed to update HIPIntegration status for {}:{}:{} - {}", serviceManagerName, hipIntegrationName, version, ex.getMessage(), ex);
        }
    }

    public IntegrationStatus getHIPIntegrationStatus(String serviceManagerName, String hipIntegrationName, String version) {
        String key = HIPRedisKeyUtil.statusKey(serviceManagerName, hipIntegrationName, version);
        String val = redisTemplate.opsForValue().get(key);
        if (val == null) return IntegrationStatus.UNREGISTERED;
        try {
            return IntegrationStatus.valueOf(val);
        } catch (Exception ex) {
            logger.warn("Unknown IntegrationStatus '{}', defaulting to ERROR", val);
            return IntegrationStatus.ERROR;
        }
    }

    // --- Throttle management (adapter-level, serviceManager-aware) ---

    public void updateThrottle(String serviceManagerName, String integrationName, String version, String adapterId, ThrottleSettings settings) {
        String key = HIPRedisKeyUtil.throttleKey(serviceManagerName, integrationName, version, adapterId);
        try {
            if (settings != null) {
                redisTemplate.opsForValue().set(key, objectMapper.writeValueAsString(settings));
                logger.info("[RUNTIME] Throttle updated for {}:{}:{}:{}: {}", serviceManagerName, integrationName, version, adapterId, settings);
            } else {
                redisTemplate.delete(key);
                logger.info("[RUNTIME] Throttle removed for {}:{}:{}:{}", serviceManagerName, integrationName, version, adapterId);
            }
        } catch (Exception e) {
            logger.error("Failed to serialize throttle settings for {}:{}:{}:{} - {}", serviceManagerName, integrationName, version, adapterId, e.getMessage(), e);
        }
    }

    public ThrottleSettings getThrottleSettings(String serviceManagerName, String integrationName, String version, String adapterId) {
        String key = HIPRedisKeyUtil.throttleKey(serviceManagerName, integrationName, version, adapterId);
        String val = redisTemplate.opsForValue().get(key);
        if (val == null) return null;
        try {
            return objectMapper.readValue(val, ThrottleSettings.class);
        } catch (Exception e) {
            logger.error("Failed to deserialize throttle settings for {}:{}:{}:{} - {}", serviceManagerName, integrationName, version, adapterId, e.getMessage(), e);
            return null;
        }
    }

    // --- SFTP Callback event (extend for other adapter callback tracking as needed) ---

    public void recordSftpCallback(String serviceManagerName, String integrationName, String version, String adapterId) {
        logger.info("[RUNTIME] SFTP callback received: {}:{}:{}:{}", serviceManagerName, integrationName, version, adapterId);
        // Optionally persist or log the fact that a callback was received
        // Could update DB, cache, or log only
    }

    // --- Utility: cleanup all runtime state for an integration (optional) ---

    public void removeAllRuntimeState(String serviceManagerName, String hipIntegrationName, String version) {
        try {
            redisTemplate.delete(HIPRedisKeyUtil.statusKey(serviceManagerName, hipIntegrationName, version));
            // To remove all throttle keys for this integration/version/adapters, you would need to scan with a pattern
            // Example (use with caution in prod!):
            // Set<String> keys = redisTemplate.keys(HIPRedisKeyUtil.throttleKey(serviceManagerName, hipIntegrationName, version, "*"));
            // if (keys != null && !keys.isEmpty()) redisTemplate.delete(keys);
            logger.info("[RUNTIME] All runtime state removed for {}:{}:{}", serviceManagerName, hipIntegrationName, version);
        } catch (Exception e) {
            logger.error("Failed to remove all runtime state for {}:{}:{} - {}", serviceManagerName, hipIntegrationName, version, e.getMessage(), e);
        }
    }
}