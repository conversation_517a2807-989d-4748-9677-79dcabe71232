package com.dell.it.hip.strategy.flows;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.EDIFlowStepConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.util.ediUtils.EdiControlNumberUpdater;
import com.dell.it.hip.util.ediUtils.EdiDocumentGenerator;
import com.dell.it.hip.util.ediUtils.EdiEnvelopeGenerator;
import com.dell.it.hip.util.ediUtils.RangeAwareEdiControlManager;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.validation.MessageFormatDetector;

import groovy.util.logging.Slf4j;

@Slf4j
@Component("ediflow")
@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
public class EDIFlowStepStrategy extends AbstractFlowStepStrategy {
	
	@Autowired
    private WiretapService wiretapService;
	
	@Autowired
	private RedissonClient redissonClient;

	@Override
    public String getType() {
        return "ediflowstep";
    }
	
	public void testEDI(Message<?> message) throws Exception {
		doExecute(message, null, null);
	}
	
	@Override
	protected List<Message<?>> doExecute(Message<?> message, FlowStepConfigRef stepConfigRef, HIPIntegrationDefinition def) throws Exception {
		   // TODO Auto-generated method stub
		   // String payload = Objects.toString(message.getPayload(), "");
		    EDIFlowStepConfig config = (EDIFlowStepConfig) def.getConfig(stepConfigRef.getPropertyRef(), EDIFlowStepConfig.class);
            Assert.notNull(config, "EDIFlowStepConfig must not be null");
		    String payload = new String((byte[]) message.getPayload(), StandardCharsets.UTF_8);
	        String format = (String) message.getHeaders().get("HIP.payload.dataformat");
	        if (format == null) {
	            format = MessageFormatDetector.detect(payload);
	        }
	        EdiDocumentGenerator edidocumentgenerator = new EdiDocumentGenerator(redissonClient, "edi:control");
	        if(format.startsWith("EDI") && edidocumentgenerator.hasValidIsaEnvelope(payload)) {
	        	 RangeAwareEdiControlManager ediManager = new RangeAwareEdiControlManager(redissonClient,
	        	            "edi:control",  // Redis key prefix
	        	            9,             // Default ID length (9 digits)
	        	            30,            // Lock wait time (seconds)
	        	            60             // Lock lease time (seconds)
	        	        );
	        	// Generate Purchase Order (850)
	             RangeAwareEdiControlManager.EdiControlRecord poRecord = ediManager.generateControlId("850", "COMPANYA", "PARTNERB");
	             System.out.println("Generated 850 Control ID: " + poRecord.getControlNumber());
	             
	             EdiEnvelopeGenerator edienvelopgenerator = new EdiEnvelopeGenerator(
	            		 redissonClient,
	                     "edi:control",  // Redis key prefix
	                     "SENDER01",      // ISA sender ID
	                     "RECEIVER01",    // ISA receiver ID
	                     "ZZ",           // ISA qualifier
	                     "PO"            // GS functional code
	                 );
	                 
	                 // 3. Raw EDI content without envelope
	                 String rawEdi = "ST*855*0001\n" +
	                                "BEG*00*00*PO12345\n" +
	                                "DTM*011*20240101\n" +
	                                "SE*3*0001";
	                 
	                 // 4. Wrap with envelope
	                 String envelopedEdi = edienvelopgenerator.ensureEnvelope(rawEdi);
	                 System.out.println(envelopedEdi);
	        }else {
	        	
	        	try {
	                // 2. Create updater
	                EdiControlNumberUpdater updater = new EdiControlNumberUpdater(
	                	redissonClient,
	                    "edi:control",
	                    "SENDER01",
	                    "RECEIVER01",
	                    "ZZ",
	                    "PO"
	                );
	                
	                // 3a. EDI with existing envelope
	                String envelopedEdi = "ISA*00**00**SENDER01*ZZ*RECEIVER01*ZZ*240101*1200*U*00401*000000123*0*P*>~" +
	                                    "GS*PO*SENDER01*RECEIVER01*20240101*1200*000000456*X*004010~" +
	                                    "ST*855*000789~...~SE*5*000789~" +
	                                    "GE*1*000000456~" +
	                                    "IEA*1*000000123~";
	                
	                String updatedEdi = updater.updateControlNumbers(envelopedEdi);
	                System.out.println("Updated EDI:\n" + updatedEdi);
	                
	                // 3b. EDI without envelope
	                String rawEdi = "ST*855*0001~BEG*00*00*PO12345~...~SE*3*0001~";
	                String wrappedEdi = updater.updateControlNumbers(rawEdi);
	                System.out.println("Wrapped EDI:\n" + wrappedEdi);
	                
	            } catch (Exception e) {
	                e.printStackTrace();
	            } finally {
	            	redissonClient.shutdown();
	            }
	        	
	        }
	        Message<?> enriched = MessageBuilder.fromMessage(message).setHeader("HIP.payload.dataformat", format).build();
	        // === Success: pass message through ===
	        wiretapService.tap(enriched, def, stepConfigRef, "info", "EDI operation completed!");
	        return Collections.singletonList(message);
	}


}
