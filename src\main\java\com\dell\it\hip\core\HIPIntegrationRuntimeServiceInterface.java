package com.dell.it.hip.core;

import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.util.ThrottleSettings;

/**
 * Interface for HIP Integration Runtime Service.
 * Provides a common contract for both Redis-enabled and database-only runtime service implementations.
 */
public interface HIPIntegrationRuntimeServiceInterface {

    // --- Status management (integration-level, not adapter-specific for now) ---

    void updateHIPIntegrationStatus(String serviceManagerName, String hipIntegrationName, String version, IntegrationStatus status);

    IntegrationStatus getHIPIntegrationStatus(String serviceManagerName, String hipIntegrationName, String version);

    // --- Throttle management (adapter-level, serviceManager-aware) ---

    void updateThrottle(String serviceManagerName, String integrationName, String version, String adapterId, ThrottleSettings settings);

    ThrottleSettings getThrottleSettings(String serviceManagerName, String integrationName, String version, String adapterId);

    // --- Cleanup ---

    void removeAllRuntimeState(String serviceManagerName, String hipIntegrationName, String version);
}
